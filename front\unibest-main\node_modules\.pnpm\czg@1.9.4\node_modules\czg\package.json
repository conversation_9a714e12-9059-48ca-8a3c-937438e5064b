{"name": "czg", "version": "1.9.4", "description": "Interactive Commitizen CLI that generate standardized git commit message", "author": "Zhengqbbb <<EMAIL>> (https://github.com/Zhengqbbb)", "license": "MIT", "homepage": "https://cz-git.qbb.sh/cli/", "repository": {"type": "git", "url": "https://github.com/Zhengqbbb/cz-git", "directory": "packages/cli"}, "bugs": {"url": "https://github.com/Zhengqbbb/cz-git/issues"}, "keywords": ["openai", "commit", "commit CLI", "commit message", "commitizen", "commitizen-cli", "cli", "cz-git", "cz-customizable"], "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "main": "lib/index.js", "types": "lib/index.d.ts", "bin": {"czg": "bin/index.js", "git-czg": "bin/index.js"}, "files": ["bin", "lib"], "engines": {"node": ">=v12.20.0"}, "devDependencies": {"cachedir": "^2.4.0", "dedent": "^1.5.1", "fs-extra": "^11.1.1", "inquirer": "8.2.4", "minimist": "^1.2.8", "rimraf": "3.0.2", "cz-git": "1.9.4"}, "scripts": {"build": "tsup", "clean": "<PERSON><PERSON><PERSON> lib", "release:changelog": "conventional-changelog -p angular -i CHANGELOG.md -s"}}