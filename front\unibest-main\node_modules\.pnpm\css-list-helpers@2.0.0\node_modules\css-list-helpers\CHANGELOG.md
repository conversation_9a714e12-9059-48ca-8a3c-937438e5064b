## 2.0.0
- **Breaking:** Remove tcomb dependency (use `typeof` instead). The only breaking change about this is that it will provide different error messages, which could potentially break existing implementations. Most people should be able to upgrade w/o changes to their code.
- Convert source code into TypeScript.
- Provide TypeScript-generated type definitions.

## 1.0.1
- Fix calling split w/o options.

## 1.0.0
- Initial release.
