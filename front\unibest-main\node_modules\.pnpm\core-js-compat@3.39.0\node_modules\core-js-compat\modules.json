["es.symbol", "es.symbol.description", "es.symbol.async-iterator", "es.symbol.has-instance", "es.symbol.is-concat-spreadable", "es.symbol.iterator", "es.symbol.match", "es.symbol.match-all", "es.symbol.replace", "es.symbol.search", "es.symbol.species", "es.symbol.split", "es.symbol.to-primitive", "es.symbol.to-string-tag", "es.symbol.unscopables", "es.error.cause", "es.error.to-string", "es.aggregate-error", "es.aggregate-error.cause", "es.array.at", "es.array.concat", "es.array.copy-within", "es.array.every", "es.array.fill", "es.array.filter", "es.array.find", "es.array.find-index", "es.array.find-last", "es.array.find-last-index", "es.array.flat", "es.array.flat-map", "es.array.for-each", "es.array.from", "es.array.includes", "es.array.index-of", "es.array.is-array", "es.array.iterator", "es.array.join", "es.array.last-index-of", "es.array.map", "es.array.of", "es.array.push", "es.array.reduce", "es.array.reduce-right", "es.array.reverse", "es.array.slice", "es.array.some", "es.array.sort", "es.array.species", "es.array.splice", "es.array.to-reversed", "es.array.to-sorted", "es.array.to-spliced", "es.array.unscopables.flat", "es.array.unscopables.flat-map", "es.array.unshift", "es.array.with", "es.array-buffer.constructor", "es.array-buffer.is-view", "es.array-buffer.slice", "es.data-view", "es.array-buffer.detached", "es.array-buffer.transfer", "es.array-buffer.transfer-to-fixed-length", "es.date.get-year", "es.date.now", "es.date.set-year", "es.date.to-gmt-string", "es.date.to-iso-string", "es.date.to-json", "es.date.to-primitive", "es.date.to-string", "es.escape", "es.function.bind", "es.function.has-instance", "es.function.name", "es.global-this", "es.iterator.constructor", "es.iterator.drop", "es.iterator.every", "es.iterator.filter", "es.iterator.find", "es.iterator.flat-map", "es.iterator.for-each", "es.iterator.from", "es.iterator.map", "es.iterator.reduce", "es.iterator.some", "es.iterator.take", "es.iterator.to-array", "es.json.stringify", "es.json.to-string-tag", "es.map", "es.map.group-by", "es.math.acosh", "es.math.asinh", "es.math.atanh", "es.math.cbrt", "es.math.clz32", "es.math.cosh", "es.math.expm1", "es.math.fround", "es.math.hypot", "es.math.imul", "es.math.log10", "es.math.log1p", "es.math.log2", "es.math.sign", "es.math.sinh", "es.math.tanh", "es.math.to-string-tag", "es.math.trunc", "es.number.constructor", "es.number.epsilon", "es.number.is-finite", "es.number.is-integer", "es.number.is-nan", "es.number.is-safe-integer", "es.number.max-safe-integer", "es.number.min-safe-integer", "es.number.parse-float", "es.number.parse-int", "es.number.to-exponential", "es.number.to-fixed", "es.number.to-precision", "es.object.assign", "es.object.create", "es.object.define-getter", "es.object.define-properties", "es.object.define-property", "es.object.define-setter", "es.object.entries", "es.object.freeze", "es.object.from-entries", "es.object.get-own-property-descriptor", "es.object.get-own-property-descriptors", "es.object.get-own-property-names", "es.object.get-prototype-of", "es.object.group-by", "es.object.has-own", "es.object.is", "es.object.is-extensible", "es.object.is-frozen", "es.object.is-sealed", "es.object.keys", "es.object.lookup-getter", "es.object.lookup-setter", "es.object.prevent-extensions", "es.object.proto", "es.object.seal", "es.object.set-prototype-of", "es.object.to-string", "es.object.values", "es.parse-float", "es.parse-int", "es.promise", "es.promise.all-settled", "es.promise.any", "es.promise.finally", "es.promise.try", "es.promise.with-resolvers", "es.reflect.apply", "es.reflect.construct", "es.reflect.define-property", "es.reflect.delete-property", "es.reflect.get", "es.reflect.get-own-property-descriptor", "es.reflect.get-prototype-of", "es.reflect.has", "es.reflect.is-extensible", "es.reflect.own-keys", "es.reflect.prevent-extensions", "es.reflect.set", "es.reflect.set-prototype-of", "es.reflect.to-string-tag", "es.regexp.constructor", "es.regexp.dot-all", "es.regexp.exec", "es.regexp.flags", "es.regexp.sticky", "es.regexp.test", "es.regexp.to-string", "es.set", "es.set.difference.v2", "es.set.intersection.v2", "es.set.is-disjoint-from.v2", "es.set.is-subset-of.v2", "es.set.is-superset-of.v2", "es.set.symmetric-difference.v2", "es.set.union.v2", "es.string.at-alternative", "es.string.code-point-at", "es.string.ends-with", "es.string.from-code-point", "es.string.includes", "es.string.is-well-formed", "es.string.iterator", "es.string.match", "es.string.match-all", "es.string.pad-end", "es.string.pad-start", "es.string.raw", "es.string.repeat", "es.string.replace", "es.string.replace-all", "es.string.search", "es.string.split", "es.string.starts-with", "es.string.substr", "es.string.to-well-formed", "es.string.trim", "es.string.trim-end", "es.string.trim-start", "es.string.anchor", "es.string.big", "es.string.blink", "es.string.bold", "es.string.fixed", "es.string.fontcolor", "es.string.fontsize", "es.string.italics", "es.string.link", "es.string.small", "es.string.strike", "es.string.sub", "es.string.sup", "es.typed-array.float32-array", "es.typed-array.float64-array", "es.typed-array.int8-array", "es.typed-array.int16-array", "es.typed-array.int32-array", "es.typed-array.uint8-array", "es.typed-array.uint8-clamped-array", "es.typed-array.uint16-array", "es.typed-array.uint32-array", "es.typed-array.at", "es.typed-array.copy-within", "es.typed-array.every", "es.typed-array.fill", "es.typed-array.filter", "es.typed-array.find", "es.typed-array.find-index", "es.typed-array.find-last", "es.typed-array.find-last-index", "es.typed-array.for-each", "es.typed-array.from", "es.typed-array.includes", "es.typed-array.index-of", "es.typed-array.iterator", "es.typed-array.join", "es.typed-array.last-index-of", "es.typed-array.map", "es.typed-array.of", "es.typed-array.reduce", "es.typed-array.reduce-right", "es.typed-array.reverse", "es.typed-array.set", "es.typed-array.slice", "es.typed-array.some", "es.typed-array.sort", "es.typed-array.subarray", "es.typed-array.to-locale-string", "es.typed-array.to-reversed", "es.typed-array.to-sorted", "es.typed-array.to-string", "es.typed-array.with", "es.unescape", "es.weak-map", "es.weak-set", "esnext.aggregate-error", "esnext.suppressed-error.constructor", "esnext.array.from-async", "esnext.array.at", "esnext.array.filter-out", "esnext.array.filter-reject", "esnext.array.find-last", "esnext.array.find-last-index", "esnext.array.group", "esnext.array.group-by", "esnext.array.group-by-to-map", "esnext.array.group-to-map", "esnext.array.is-template-object", "esnext.array.last-index", "esnext.array.last-item", "esnext.array.to-reversed", "esnext.array.to-sorted", "esnext.array.to-spliced", "esnext.array.unique-by", "esnext.array.with", "esnext.array-buffer.detached", "esnext.array-buffer.transfer", "esnext.array-buffer.transfer-to-fixed-length", "esnext.async-disposable-stack.constructor", "esnext.async-iterator.constructor", "esnext.async-iterator.as-indexed-pairs", "esnext.async-iterator.async-dispose", "esnext.async-iterator.drop", "esnext.async-iterator.every", "esnext.async-iterator.filter", "esnext.async-iterator.find", "esnext.async-iterator.flat-map", "esnext.async-iterator.for-each", "esnext.async-iterator.from", "esnext.async-iterator.indexed", "esnext.async-iterator.map", "esnext.async-iterator.reduce", "esnext.async-iterator.some", "esnext.async-iterator.take", "esnext.async-iterator.to-array", "esnext.bigint.range", "esnext.composite-key", "esnext.composite-symbol", "esnext.data-view.get-float16", "esnext.data-view.get-uint8-clamped", "esnext.data-view.set-float16", "esnext.data-view.set-uint8-clamped", "esnext.disposable-stack.constructor", "esnext.function.demethodize", "esnext.function.is-callable", "esnext.function.is-constructor", "esnext.function.metadata", "esnext.function.un-this", "esnext.global-this", "esnext.iterator.constructor", "esnext.iterator.as-indexed-pairs", "esnext.iterator.concat", "esnext.iterator.dispose", "esnext.iterator.drop", "esnext.iterator.every", "esnext.iterator.filter", "esnext.iterator.find", "esnext.iterator.flat-map", "esnext.iterator.for-each", "esnext.iterator.from", "esnext.iterator.indexed", "esnext.iterator.map", "esnext.iterator.range", "esnext.iterator.reduce", "esnext.iterator.some", "esnext.iterator.take", "esnext.iterator.to-array", "esnext.iterator.to-async", "esnext.json.is-raw-json", "esnext.json.parse", "esnext.json.raw-json", "esnext.map.delete-all", "esnext.map.emplace", "esnext.map.every", "esnext.map.filter", "esnext.map.find", "esnext.map.find-key", "esnext.map.from", "esnext.map.get-or-insert", "esnext.map.get-or-insert-computed", "esnext.map.group-by", "esnext.map.includes", "esnext.map.key-by", "esnext.map.key-of", "esnext.map.map-keys", "esnext.map.map-values", "esnext.map.merge", "esnext.map.of", "esnext.map.reduce", "esnext.map.some", "esnext.map.update", "esnext.map.update-or-insert", "esnext.map.upsert", "esnext.math.clamp", "esnext.math.deg-per-rad", "esnext.math.degrees", "esnext.math.fscale", "esnext.math.f16round", "esnext.math.iaddh", "esnext.math.imulh", "esnext.math.isubh", "esnext.math.rad-per-deg", "esnext.math.radians", "esnext.math.scale", "esnext.math.seeded-prng", "esnext.math.signbit", "esnext.math.sum-precise", "esnext.math.umulh", "esnext.number.from-string", "esnext.number.range", "esnext.object.has-own", "esnext.object.iterate-entries", "esnext.object.iterate-keys", "esnext.object.iterate-values", "esnext.object.group-by", "esnext.observable", "esnext.promise.all-settled", "esnext.promise.any", "esnext.promise.try", "esnext.promise.with-resolvers", "esnext.reflect.define-metadata", "esnext.reflect.delete-metadata", "esnext.reflect.get-metadata", "esnext.reflect.get-metadata-keys", "esnext.reflect.get-own-metadata", "esnext.reflect.get-own-metadata-keys", "esnext.reflect.has-metadata", "esnext.reflect.has-own-metadata", "esnext.reflect.metadata", "esnext.regexp.escape", "esnext.set.add-all", "esnext.set.delete-all", "esnext.set.difference.v2", "esnext.set.difference", "esnext.set.every", "esnext.set.filter", "esnext.set.find", "esnext.set.from", "esnext.set.intersection.v2", "esnext.set.intersection", "esnext.set.is-disjoint-from.v2", "esnext.set.is-disjoint-from", "esnext.set.is-subset-of.v2", "esnext.set.is-subset-of", "esnext.set.is-superset-of.v2", "esnext.set.is-superset-of", "esnext.set.join", "esnext.set.map", "esnext.set.of", "esnext.set.reduce", "esnext.set.some", "esnext.set.symmetric-difference.v2", "esnext.set.symmetric-difference", "esnext.set.union.v2", "esnext.set.union", "esnext.string.at", "esnext.string.cooked", "esnext.string.code-points", "esnext.string.dedent", "esnext.string.is-well-formed", "esnext.string.match-all", "esnext.string.replace-all", "esnext.string.to-well-formed", "esnext.symbol.async-dispose", "esnext.symbol.custom-matcher", "esnext.symbol.dispose", "esnext.symbol.is-registered-symbol", "esnext.symbol.is-registered", "esnext.symbol.is-well-known-symbol", "esnext.symbol.is-well-known", "esnext.symbol.matcher", "esnext.symbol.metadata", "esnext.symbol.metadata-key", "esnext.symbol.observable", "esnext.symbol.pattern-match", "esnext.symbol.replace-all", "esnext.typed-array.from-async", "esnext.typed-array.at", "esnext.typed-array.filter-out", "esnext.typed-array.filter-reject", "esnext.typed-array.find-last", "esnext.typed-array.find-last-index", "esnext.typed-array.group-by", "esnext.typed-array.to-reversed", "esnext.typed-array.to-sorted", "esnext.typed-array.to-spliced", "esnext.typed-array.unique-by", "esnext.typed-array.with", "esnext.uint8-array.from-base64", "esnext.uint8-array.from-hex", "esnext.uint8-array.set-from-base64", "esnext.uint8-array.set-from-hex", "esnext.uint8-array.to-base64", "esnext.uint8-array.to-hex", "esnext.weak-map.delete-all", "esnext.weak-map.from", "esnext.weak-map.of", "esnext.weak-map.emplace", "esnext.weak-map.get-or-insert", "esnext.weak-map.get-or-insert-computed", "esnext.weak-map.upsert", "esnext.weak-set.add-all", "esnext.weak-set.delete-all", "esnext.weak-set.from", "esnext.weak-set.of", "web.atob", "web.btoa", "web.dom-collections.for-each", "web.dom-collections.iterator", "web.dom-exception.constructor", "web.dom-exception.stack", "web.dom-exception.to-string-tag", "web.immediate", "web.queue-microtask", "web.self", "web.structured-clone", "web.timers", "web.url", "web.url.can-parse", "web.url.parse", "web.url.to-json", "web.url-search-params", "web.url-search-params.delete", "web.url-search-params.has", "web.url-search-params.size"]